Archive member included to satisfy reference by file (symbol)

F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
                              obj/main.o (__aeabi_uidiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
                              obj/main.o (__aeabi_idiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o) (__aeabi_idiv0)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
                              obj/BSPPrint.o (__aeabi_d2ulz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_dmul)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_dsub)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o) (__aeabi_d2uiz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
                              obj/stream_buffer.o (memset)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
                              obj/tasks.o (sprintf)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
                              obj/tasks.o (stpcpy)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
                              obj/tasks.o (strlen)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o) (_svfprintf_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_dtoa_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_free_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o) (_impure_ptr)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_localeconv_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (_malloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (memchr)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o) (__malloc_lock)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o) (_Balloc)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (frexp)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o) (_sbrk_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (strncpy)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__ssprint_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o) (_calloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o) (__global_locale)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o) (__retarget_lock_acquire_recursive)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (__ascii_mbtowc)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o) (memmove)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o) (_realloc_r)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o) (errno)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (strcmp)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (__ascii_wctomb)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o) (_ctype_)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o) (__aeabi_ddiv)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_dcmpeq)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_dcmpun)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_d2iz)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o) (__aeabi_uldivmod)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o) (__udivmoddi4)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o) (__clzdi2)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o) (__clzsi2)
F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
                              F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o) (_sbrk)

Allocating common symbols
Common symbol       size              file

rdata               0x4               obj/gic_handle_irq.o
__lock___atexit_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___arc4random_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
StartTask_Handler   0x4               obj/main.o
errno               0x4               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
rlt                 0x1               obj/timer.o
fiq_num             0x4               obj/gic_handle_irq.o
xQueueRegistry      0x40              obj/queue.o
error_temp          0x4               obj/FreeRTOS_aux.o
__lock___env_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___sinit_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
irq_num             0x4               obj/gic_handle_irq.o
__lock___malloc_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___at_quick_exit_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___dd_hash_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
__lock___tz_mutex   0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
rdata2              0x4               obj/gic_handle_irq.o
__lock___sfp_recursive_mutex
                    0x1               F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
rlt_tmp             0x1               obj/apb_timer.o

Discarded input sections

 .text          0x00000000      0x2a0 obj/lowlevel.o
 .data          0x00000000        0x0 obj/lowlevel.o
 .bss           0x00000000        0x0 obj/lowlevel.o
 .debug_line    0x00000000       0xdd obj/lowlevel.o
 .debug_info    0x00000000       0x26 obj/lowlevel.o
 .debug_abbrev  0x00000000       0x14 obj/lowlevel.o
 .debug_aranges
                0x00000000       0x20 obj/lowlevel.o
 .debug_str     0x00000000       0x4a obj/lowlevel.o
 .ARM.attributes
                0x00000000       0x25 obj/lowlevel.o
 .text          0x00000000       0x38 obj/mem_clear.o
 .data          0x00000000        0x0 obj/mem_clear.o
 .bss           0x00000000        0x0 obj/mem_clear.o
 .debug_line    0x00000000       0x54 obj/mem_clear.o
 .debug_info    0x00000000       0x26 obj/mem_clear.o
 .debug_abbrev  0x00000000       0x14 obj/mem_clear.o
 .debug_aranges
                0x00000000       0x20 obj/mem_clear.o
 .debug_str     0x00000000       0x4b obj/mem_clear.o
 .ARM.attributes
                0x00000000       0x25 obj/mem_clear.o
 .text          0x00000000       0x9c obj/start.o
 .data          0x00000000        0x0 obj/start.o
 .bss           0x00000000        0x0 obj/start.o
 .debug_line    0x00000000       0x76 obj/start.o
 .debug_info    0x00000000       0x26 obj/start.o
 .debug_abbrev  0x00000000       0x14 obj/start.o
 .debug_aranges
                0x00000000       0x20 obj/start.o
 .debug_str     0x00000000       0x47 obj/start.o
 .ARM.attributes
                0x00000000       0x25 obj/start.o
 .text          0x00000000        0x0 obj/vectors.o
 .data          0x00000000        0x0 obj/vectors.o
 .bss           0x00000000        0x0 obj/vectors.o
 .vectors       0x00000000      0x244 obj/vectors.o
 .debug_line    0x00000000       0x7a obj/vectors.o
 .debug_info    0x00000000       0x26 obj/vectors.o
 .debug_abbrev  0x00000000       0x14 obj/vectors.o
 .debug_aranges
                0x00000000       0x20 obj/vectors.o
 .debug_str     0x00000000       0x49 obj/vectors.o
 .ARM.attributes
                0x00000000       0x25 obj/vectors.o
 .text          0x00000000      0x2e0 obj/portASM.o
 .data          0x00000000        0x0 obj/portASM.o
 .bss           0x00000000        0x0 obj/portASM.o
 .debug_line    0x00000000       0xaf obj/portASM.o
 .debug_info    0x00000000       0x26 obj/portASM.o
 .debug_abbrev  0x00000000       0x14 obj/portASM.o
 .debug_aranges
                0x00000000       0x20 obj/portASM.o
 .debug_str     0x00000000       0x5b obj/portASM.o
 .ARM.attributes
                0x00000000       0x27 obj/portASM.o
 .text          0x00000000       0xa0 obj/FreeRTOS_aux.o
 .data          0x00000000        0x0 obj/FreeRTOS_aux.o
 .bss           0x00000000     0x30f8 obj/FreeRTOS_aux.o
 .rodata.str1.4
                0x00000000       0x47 obj/FreeRTOS_aux.o
 .debug_info    0x00000000      0xdad obj/FreeRTOS_aux.o
 .debug_abbrev  0x00000000      0x2fa obj/FreeRTOS_aux.o
 .debug_loc     0x00000000       0x5a obj/FreeRTOS_aux.o
 .debug_aranges
                0x00000000       0x20 obj/FreeRTOS_aux.o
 .debug_ranges  0x00000000       0x28 obj/FreeRTOS_aux.o
 .debug_line    0x00000000      0x31f obj/FreeRTOS_aux.o
 .debug_str     0x00000000      0x831 obj/FreeRTOS_aux.o
 .comment       0x00000000       0x7a obj/FreeRTOS_aux.o
 .debug_frame   0x00000000       0x6c obj/FreeRTOS_aux.o
 .ARM.attributes
                0x00000000       0x37 obj/FreeRTOS_aux.o
 COMMON         0x00000000        0x4 obj/FreeRTOS_aux.o
 .text          0x00000000      0x45c obj/main.o
 .data          0x00000000        0x0 obj/main.o
 .bss           0x00000000        0x8 obj/main.o
 .rodata.str1.4
                0x00000000       0x53 obj/main.o
 .rodata        0x00000000    0x1c028 obj/main.o
 .debug_info    0x00000000     0x108f obj/main.o
 .debug_abbrev  0x00000000      0x4b6 obj/main.o
 .debug_loc     0x00000000      0x5d0 obj/main.o
 .debug_aranges
                0x00000000       0x20 obj/main.o
 .debug_ranges  0x00000000       0xa8 obj/main.o
 .debug_line    0x00000000      0x6ea obj/main.o
 .debug_str     0x00000000      0x80c obj/main.o
 .comment       0x00000000       0x7a obj/main.o
 .debug_frame   0x00000000      0x164 obj/main.o
 .ARM.attributes
                0x00000000       0x37 obj/main.o
 COMMON         0x00000000        0x4 obj/main.o
 .text          0x00000000       0x20 obj/apb_timer.o
 .data          0x00000000        0x0 obj/apb_timer.o
 .bss           0x00000000        0x0 obj/apb_timer.o
 .debug_info    0x00000000       0xf3 obj/apb_timer.o
 .debug_abbrev  0x00000000       0xb8 obj/apb_timer.o
 .debug_aranges
                0x00000000       0x20 obj/apb_timer.o
 .debug_line    0x00000000       0x8e obj/apb_timer.o
 .debug_str     0x00000000      0x1b0 obj/apb_timer.o
 .comment       0x00000000       0x7a obj/apb_timer.o
 .debug_frame   0x00000000       0x20 obj/apb_timer.o
 .ARM.attributes
                0x00000000       0x37 obj/apb_timer.o
 COMMON         0x00000000        0x1 obj/apb_timer.o
 .text          0x00000000      0x2d0 obj/BSPPrint.o
 .data          0x00000000        0x0 obj/BSPPrint.o
 .bss           0x00000000        0x0 obj/BSPPrint.o
 .rodata        0x00000000       0x10 obj/BSPPrint.o
 .debug_info    0x00000000      0x4f8 obj/BSPPrint.o
 .debug_abbrev  0x00000000      0x278 obj/BSPPrint.o
 .debug_loc     0x00000000      0x3d9 obj/BSPPrint.o
 .debug_aranges
                0x00000000       0x20 obj/BSPPrint.o
 .debug_ranges  0x00000000       0x88 obj/BSPPrint.o
 .debug_line    0x00000000      0x416 obj/BSPPrint.o
 .debug_str     0x00000000      0x360 obj/BSPPrint.o
 .comment       0x00000000       0x7a obj/BSPPrint.o
 .debug_frame   0x00000000       0x94 obj/BSPPrint.o
 .ARM.attributes
                0x00000000       0x37 obj/BSPPrint.o
 .text          0x00000000      0x308 obj/gic_handle_irq.o
 .data          0x00000000        0x0 obj/gic_handle_irq.o
 .bss           0x00000000        0x4 obj/gic_handle_irq.o
 .rodata.str1.4
                0x00000000      0x32a obj/gic_handle_irq.o
 .debug_info    0x00000000      0x506 obj/gic_handle_irq.o
 .debug_abbrev  0x00000000      0x18b obj/gic_handle_irq.o
 .debug_loc     0x00000000       0xba obj/gic_handle_irq.o
 .debug_aranges
                0x00000000       0x20 obj/gic_handle_irq.o
 .debug_line    0x00000000      0x2f1 obj/gic_handle_irq.o
 .debug_str     0x00000000      0x2e3 obj/gic_handle_irq.o
 .comment       0x00000000       0x7a obj/gic_handle_irq.o
 .debug_frame   0x00000000       0xd4 obj/gic_handle_irq.o
 .ARM.attributes
                0x00000000       0x37 obj/gic_handle_irq.o
 COMMON         0x00000000       0x10 obj/gic_handle_irq.o
 .text          0x00000000      0x2e8 obj/gic_irq.o
 .data          0x00000000        0x0 obj/gic_irq.o
 .bss           0x00000000        0x0 obj/gic_irq.o
 .rodata.str1.4
                0x00000000       0x30 obj/gic_irq.o
 .debug_info    0x00000000      0x933 obj/gic_irq.o
 .debug_abbrev  0x00000000      0x274 obj/gic_irq.o
 .debug_loc     0x00000000      0x7de obj/gic_irq.o
 .debug_aranges
                0x00000000       0x20 obj/gic_irq.o
 .debug_ranges  0x00000000      0x2b8 obj/gic_irq.o
 .debug_line    0x00000000      0x421 obj/gic_irq.o
 .debug_str     0x00000000      0x34e obj/gic_irq.o
 .comment       0x00000000       0x7a obj/gic_irq.o
 .debug_frame   0x00000000      0x140 obj/gic_irq.o
 .ARM.attributes
                0x00000000       0x37 obj/gic_irq.o
 .text          0x00000000      0x388 obj/irq_gic.o
 .data          0x00000000        0x0 obj/irq_gic.o
 .bss           0x00000000      0x280 obj/irq_gic.o
 .rodata.str1.4
                0x00000000       0x27 obj/irq_gic.o
 .debug_info    0x00000000      0x683 obj/irq_gic.o
 .debug_abbrev  0x00000000      0x368 obj/irq_gic.o
 .debug_loc     0x00000000      0x472 obj/irq_gic.o
 .debug_aranges
                0x00000000       0x20 obj/irq_gic.o
 .debug_ranges  0x00000000       0x70 obj/irq_gic.o
 .debug_line    0x00000000      0x4e6 obj/irq_gic.o
 .debug_str     0x00000000      0x373 obj/irq_gic.o
 .comment       0x00000000       0x7a obj/irq_gic.o
 .debug_frame   0x00000000      0x188 obj/irq_gic.o
 .ARM.attributes
                0x00000000       0x37 obj/irq_gic.o
 .text          0x00000000      0x67c obj/mprintf.o
 .data          0x00000000        0x0 obj/mprintf.o
 .bss           0x00000000        0x0 obj/mprintf.o
 .rodata.str1.4
                0x00000000       0x2a obj/mprintf.o
 .debug_info    0x00000000     0x12b3 obj/mprintf.o
 .debug_abbrev  0x00000000      0x43f obj/mprintf.o
 .debug_loc     0x00000000      0xa8d obj/mprintf.o
 .debug_aranges
                0x00000000       0x20 obj/mprintf.o
 .debug_ranges  0x00000000      0x120 obj/mprintf.o
 .debug_line    0x00000000      0x6c1 obj/mprintf.o
 .debug_str     0x00000000      0x6f7 obj/mprintf.o
 .comment       0x00000000       0x7a obj/mprintf.o
 .debug_frame   0x00000000      0x158 obj/mprintf.o
 .ARM.attributes
                0x00000000       0x37 obj/mprintf.o
 .text          0x00000000      0xac8 obj/sram_on_chip_test.o
 .data          0x00000000        0x0 obj/sram_on_chip_test.o
 .bss           0x00000000        0x0 obj/sram_on_chip_test.o
 .rodata.str1.4
                0x00000000      0x5ff obj/sram_on_chip_test.o
 .rodata        0x00000000       0x10 obj/sram_on_chip_test.o
 .debug_info    0x00000000      0xd01 obj/sram_on_chip_test.o
 .debug_abbrev  0x00000000      0x20f obj/sram_on_chip_test.o
 .debug_loc     0x00000000      0x587 obj/sram_on_chip_test.o
 .debug_aranges
                0x00000000       0x20 obj/sram_on_chip_test.o
 .debug_ranges  0x00000000       0x48 obj/sram_on_chip_test.o
 .debug_line    0x00000000      0xb6e obj/sram_on_chip_test.o
 .debug_str     0x00000000      0x2cc obj/sram_on_chip_test.o
 .comment       0x00000000       0x7a obj/sram_on_chip_test.o
 .debug_frame   0x00000000      0x118 obj/sram_on_chip_test.o
 .ARM.attributes
                0x00000000       0x37 obj/sram_on_chip_test.o
 .text          0x00000000      0x1ac obj/timer.o
 .data          0x00000000        0x0 obj/timer.o
 .bss           0x00000000        0x0 obj/timer.o
 .debug_info    0x00000000      0x336 obj/timer.o
 .debug_abbrev  0x00000000      0x19f obj/timer.o
 .debug_aranges
                0x00000000       0x20 obj/timer.o
 .debug_line    0x00000000      0x282 obj/timer.o
 .debug_str     0x00000000      0x2cb obj/timer.o
 .comment       0x00000000       0x7a obj/timer.o
 .debug_frame   0x00000000       0x78 obj/timer.o
 .ARM.attributes
                0x00000000       0x37 obj/timer.o
 COMMON         0x00000000        0x1 obj/timer.o
 .text          0x00000000       0xec obj/uart.o
 .data          0x00000000        0x0 obj/uart.o
 .bss           0x00000000        0x0 obj/uart.o
 .debug_info    0x00000000      0x29f obj/uart.o
 .debug_abbrev  0x00000000      0x1ad obj/uart.o
 .debug_loc     0x00000000      0x11d obj/uart.o
 .debug_aranges
                0x00000000       0x20 obj/uart.o
 .debug_ranges  0x00000000       0x18 obj/uart.o
 .debug_line    0x00000000      0x15a obj/uart.o
 .debug_str     0x00000000      0x1ff obj/uart.o
 .comment       0x00000000       0x7a obj/uart.o
 .debug_frame   0x00000000       0x70 obj/uart.o
 .ARM.attributes
                0x00000000       0x37 obj/uart.o
 .text          0x00000000        0x0 obj/croutine.o
 .data          0x00000000        0x0 obj/croutine.o
 .bss           0x00000000        0x0 obj/croutine.o
 .debug_info    0x00000000       0xd8 obj/croutine.o
 .debug_abbrev  0x00000000       0x9a obj/croutine.o
 .debug_aranges
                0x00000000       0x18 obj/croutine.o
 .debug_line    0x00000000       0x4a obj/croutine.o
 .debug_str     0x00000000      0x1c8 obj/croutine.o
 .comment       0x00000000       0x7a obj/croutine.o
 .ARM.attributes
                0x00000000       0x3d obj/croutine.o
 .text          0x00000000      0x46c obj/event_groups.o
 .data          0x00000000        0x0 obj/event_groups.o
 .bss           0x00000000        0x0 obj/event_groups.o
 .debug_info    0x00000000     0x172d obj/event_groups.o
 .debug_abbrev  0x00000000      0x3e8 obj/event_groups.o
 .debug_loc     0x00000000     0x106a obj/event_groups.o
 .debug_aranges
                0x00000000       0x20 obj/event_groups.o
 .debug_ranges  0x00000000       0x60 obj/event_groups.o
 .debug_line    0x00000000      0xb3a obj/event_groups.o
 .debug_str     0x00000000      0xb73 obj/event_groups.o
 .comment       0x00000000       0x7a obj/event_groups.o
 .debug_frame   0x00000000      0x14c obj/event_groups.o
 .ARM.attributes
                0x00000000       0x37 obj/event_groups.o
 .text          0x00000000       0xec obj/list.o
 .data          0x00000000        0x0 obj/list.o
 .bss           0x00000000        0x0 obj/list.o
 .debug_info    0x00000000      0xbe4 obj/list.o
 .debug_abbrev  0x00000000      0x26b obj/list.o
 .debug_loc     0x00000000       0x93 obj/list.o
 .debug_aranges
                0x00000000       0x20 obj/list.o
 .debug_line    0x00000000      0x3f6 obj/list.o
 .debug_str     0x00000000      0x6fd obj/list.o
 .comment       0x00000000       0x7a obj/list.o
 .debug_frame   0x00000000       0x68 obj/list.o
 .ARM.attributes
                0x00000000       0x37 obj/list.o
 .text          0x00000000     0x1390 obj/queue.o
 .data          0x00000000        0x0 obj/queue.o
 .bss           0x00000000        0x0 obj/queue.o
 .debug_info    0x00000000     0x3103 obj/queue.o
 .debug_abbrev  0x00000000      0x4ae obj/queue.o
 .debug_loc     0x00000000     0x2abc obj/queue.o
 .debug_aranges
                0x00000000       0x20 obj/queue.o
 .debug_ranges  0x00000000      0x2f8 obj/queue.o
 .debug_line    0x00000000     0x22a5 obj/queue.o
 .debug_str     0x00000000     0x1115 obj/queue.o
 .comment       0x00000000       0x7a obj/queue.o
 .debug_frame   0x00000000      0x428 obj/queue.o
 .ARM.attributes
                0x00000000       0x37 obj/queue.o
 COMMON         0x00000000       0x40 obj/queue.o
 .text          0x00000000      0xbb0 obj/stream_buffer.o
 .data          0x00000000        0x0 obj/stream_buffer.o
 .bss           0x00000000        0x0 obj/stream_buffer.o
 .debug_info    0x00000000     0x2693 obj/stream_buffer.o
 .debug_abbrev  0x00000000      0x446 obj/stream_buffer.o
 .debug_loc     0x00000000     0x282a obj/stream_buffer.o
 .debug_aranges
                0x00000000       0x20 obj/stream_buffer.o
 .debug_ranges  0x00000000       0xf8 obj/stream_buffer.o
 .debug_line    0x00000000     0x1782 obj/stream_buffer.o
 .debug_str     0x00000000      0xe14 obj/stream_buffer.o
 .comment       0x00000000       0x7a obj/stream_buffer.o
 .debug_frame   0x00000000      0x32c obj/stream_buffer.o
 .ARM.attributes
                0x00000000       0x37 obj/stream_buffer.o
 .text          0x00000000     0x2758 obj/tasks.o
 .data          0x00000000        0x4 obj/tasks.o
 .bss           0x00000000      0x100 obj/tasks.o
 .rodata.str1.4
                0x00000000       0x17 obj/tasks.o
 .rodata        0x00000000        0x5 obj/tasks.o
 .debug_info    0x00000000     0x4355 obj/tasks.o
 .debug_abbrev  0x00000000      0x5a0 obj/tasks.o
 .debug_loc     0x00000000     0x33b6 obj/tasks.o
 .debug_aranges
                0x00000000       0x20 obj/tasks.o
 .debug_ranges  0x00000000      0x788 obj/tasks.o
 .debug_line    0x00000000     0x31e1 obj/tasks.o
 .debug_str     0x00000000     0x18cc obj/tasks.o
 .comment       0x00000000       0x7a obj/tasks.o
 .debug_frame   0x00000000      0x7b8 obj/tasks.o
 .ARM.attributes
                0x00000000       0x37 obj/tasks.o
 .text          0x00000000      0x7b8 obj/timers.o
 .data          0x00000000        0x0 obj/timers.o
 .bss           0x00000000      0x104 obj/timers.o
 .rodata.str1.4
                0x00000000       0x10 obj/timers.o
 .debug_info    0x00000000     0x2214 obj/timers.o
 .debug_abbrev  0x00000000      0x43d obj/timers.o
 .debug_loc     0x00000000     0x145f obj/timers.o
 .debug_aranges
                0x00000000       0x20 obj/timers.o
 .debug_ranges  0x00000000      0x140 obj/timers.o
 .debug_line    0x00000000      0xe29 obj/timers.o
 .debug_str     0x00000000      0xfb0 obj/timers.o
 .comment       0x00000000       0x7a obj/timers.o
 .debug_frame   0x00000000      0x29c obj/timers.o
 .ARM.attributes
                0x00000000       0x37 obj/timers.o
 .text          0x00000000      0x650 obj/port.o
 .data          0x00000000        0x4 obj/port.o
 .bss           0x00000000        0xc obj/port.o
 .rodata        0x00000000       0x10 obj/port.o
 .debug_info    0x00000000      0xe8d obj/port.o
 .debug_abbrev  0x00000000      0x390 obj/port.o
 .debug_loc     0x00000000      0x2ee obj/port.o
 .debug_aranges
                0x00000000       0x20 obj/port.o
 .debug_ranges  0x00000000       0x20 obj/port.o
 .debug_line    0x00000000      0x928 obj/port.o
 .debug_str     0x00000000      0x87f obj/port.o
 .comment       0x00000000       0x7a obj/port.o
 .debug_frame   0x00000000       0xf8 obj/port.o
 .ARM.attributes
                0x00000000       0x37 obj/port.o
 .text          0x00000000      0x374 obj/heap_4.o
 .data          0x00000000        0x0 obj/heap_4.o
 .bss           0x00000000     0xc81c obj/heap_4.o
 .debug_info    0x00000000      0xf35 obj/heap_4.o
 .debug_abbrev  0x00000000      0x3dc obj/heap_4.o
 .debug_loc     0x00000000      0x57d obj/heap_4.o
 .debug_aranges
                0x00000000       0x20 obj/heap_4.o
 .debug_line    0x00000000      0x772 obj/heap_4.o
 .debug_str     0x00000000      0x938 obj/heap_4.o
 .comment       0x00000000       0x7a obj/heap_4.o
 .debug_frame   0x00000000       0xe4 obj/heap_4.o
 .ARM.attributes
                0x00000000       0x37 obj/heap_4.o
 .text          0x00000000      0x114 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .debug_frame   0x00000000       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivsi3.o)
 .text          0x00000000      0x148 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .debug_frame   0x00000000       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_divsi3.o)
 .text          0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_dvmd_tls.o)
 .text          0x00000000       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .debug_frame   0x00000000       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_fixunsdfdi.o)
 .text          0x00000000      0x290 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .debug_frame   0x00000000       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldf3.o)
 .text          0x00000000      0x424 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .debug_frame   0x00000000       0xac F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_addsubdf3.o)
 .text          0x00000000       0x54 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .debug_frame   0x00000000       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixunsdfsi.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .text.memset   0x00000000      0x11c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .debug_frame   0x00000000       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memset.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .text._sprintf_r
                0x00000000       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .text.sprintf  0x00000000       0x6c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .debug_frame   0x00000000       0x6c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .text.stpcpy   0x00000000       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .debug_frame   0x00000000       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-stpcpy.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .text.strlen   0x00000000       0x60 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .debug_frame   0x00000000       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strlen-stub.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .rodata._svfprintf_r.str1.4
                0x00000000       0x42 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .text._svfprintf_r
                0x00000000     0x27a0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .rodata.blanks.7345
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .rodata.zeroes.7346
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .debug_frame   0x00000000       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .text.quorem   0x00000000      0x1c0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .rodata._dtoa_r.str1.4
                0x00000000       0x12 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .text._dtoa_r  0x00000000     0x16d4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .debug_frame   0x00000000       0xc0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-dtoa.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .text._malloc_trim_r
                0x00000000      0x100 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .text._free_r  0x00000000      0x2ec F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .debug_frame   0x00000000       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-freer.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .data._impure_ptr
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .data.impure_data
                0x00000000      0x428 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .rodata._global_impure_ptr
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .ARM.attributes
                0x00000000       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-impure.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text.__localeconv_l
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text._localeconv_r
                0x00000000        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text.localeconv
                0x00000000        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .debug_frame   0x00000000       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-localeconv.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .text._malloc_r
                0x00000000      0x7d4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .bss.__malloc_current_mallinfo
                0x00000000       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .bss.__malloc_max_sbrked_mem
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .bss.__malloc_max_total_mem
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .bss.__malloc_top_pad
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .data.__malloc_av_
                0x00000000      0x408 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .data.__malloc_sbrk_base
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .data.__malloc_trim_threshold
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .debug_frame   0x00000000       0x74 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mallocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .text.memchr   0x00000000       0xf8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .debug_frame   0x00000000       0x4c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memchr-stub.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .text.__malloc_lock
                0x00000000       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .text.__malloc_unlock
                0x00000000       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .debug_frame   0x00000000       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mlock.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text._Balloc  0x00000000       0x8c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text._Bfree   0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__multadd
                0x00000000       0xd0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__s2b    0x00000000       0xf0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__hi0bits
                0x00000000       0x58 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__lo0bits
                0x00000000       0xa0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__i2b    0x00000000       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__multiply
                0x00000000      0x1f0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__pow5mult
                0x00000000      0x104 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__lshift
                0x00000000      0x118 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__mcmp   0x00000000       0x60 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__mdiff  0x00000000      0x1e4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__ulp    0x00000000       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__b2d    0x00000000       0xe0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__d2b    0x00000000      0x118 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__ratio  0x00000000       0x84 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text._mprec_log10
                0x00000000       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__copybits
                0x00000000       0x70 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text.__any_on
                0x00000000       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .rodata.__mprec_bigtens
                0x00000000       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .rodata.__mprec_tens
                0x00000000       0xc8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .rodata.__mprec_tinytens
                0x00000000       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .rodata.p05.6115
                0x00000000        0xc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .debug_frame   0x00000000      0x2fc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mprec.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .text.frexp    0x00000000       0xa4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .debug_frame   0x00000000       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-s_frexp.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .text._sbrk_r  0x00000000       0x44 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .debug_frame   0x00000000       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-sbrkr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .text.strncpy  0x00000000       0xcc F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .debug_frame   0x00000000       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strncpy.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .text.__ssprint_r
                0x00000000      0x1a4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata._svfiprintf_r.str1.4
                0x00000000       0x2f F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .text._svfiprintf_r
                0x00000000     0x1114 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata.blanks.7324
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .rodata.zeroes.7325
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .debug_frame   0x00000000       0xa0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-svfiprintf.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .text._calloc_r
                0x00000000       0x9c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .debug_frame   0x00000000       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-callocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .rodata._setlocale_r.str1.4
                0x00000000        0xd F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text._setlocale_r
                0x00000000       0x68 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text.__locale_mb_cur_max
                0x00000000       0x10 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text.setlocale
                0x00000000       0x18 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .rodata.str1.4
                0x00000000        0x2 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .bss._PathLocale
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .data.__global_locale
                0x00000000      0x16c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .debug_frame   0x00000000       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-locale.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_init_recursive
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_acquire_recursive
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text.__retarget_lock_release_recursive
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .debug_frame   0x00000000       0xb0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 COMMON         0x00000000       0x21 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-lock.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .text._mbtowc_r
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .text.__ascii_mbtowc
                0x00000000       0x44 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .debug_frame   0x00000000       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-mbtowc_r.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .text.memmove  0x00000000      0x158 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .debug_frame   0x00000000       0x34 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-memmove.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .text._realloc_r
                0x00000000      0x594 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .debug_frame   0x00000000       0x70 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reallocr.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text.cleanup_glue
                0x00000000       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text._reclaim_reent
                0x00000000       0xf0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .debug_frame   0x00000000       0x64 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 COMMON         0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-reent.o)
 .text          0x00000000      0x224 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .debug_frame   0x00000000       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .ARM.attributes
                0x00000000       0x1a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-strcmp.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .text._wctomb_r
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .text.__ascii_wctomb
                0x00000000       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .debug_frame   0x00000000       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-wctomb_r.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .rodata._ctype_
                0x00000000      0x101 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .ARM.attributes
                0x00000000       0x30 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a(lib_a-ctype_.o)
 .text          0x00000000      0x49c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .debug_frame   0x00000000       0x50 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_muldivdf3.o)
 .text          0x00000000      0x144 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .debug_frame   0x00000000       0xc4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_cmpdf2.o)
 .text          0x00000000       0x38 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .debug_frame   0x00000000       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_unorddf2.o)
 .text          0x00000000       0x5c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .debug_frame   0x00000000       0x24 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_arm_fixdfsi.o)
 .text          0x00000000       0x3c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x00000000       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_aeabi_uldivmod.o)
 .text          0x00000000      0x130 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .ARM.exidx     0x00000000        0x8 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .debug_frame   0x00000000       0x40 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_udivmoddi4.o)
 .text          0x00000000       0x28 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzdi2.o)
 .text          0x00000000       0x48 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .ARM.attributes
                0x00000000       0x1c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a(_clzsi2.o)
 .text          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .data          0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .bss           0x00000000        0x0 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .text._sbrk    0x00000000       0x2c F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .bss.heap_end.4144
                0x00000000        0x4 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .debug_frame   0x00000000       0x20 F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)
 .ARM.attributes
                0x00000000       0x2a F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a(sbrk.o)

Memory Configuration

Name             Origin             Length             Attributes
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD obj/lowlevel.o
LOAD obj/mem_clear.o
LOAD obj/start.o
LOAD obj/vectors.o
LOAD obj/portASM.o
LOAD obj/FreeRTOS_aux.o
LOAD obj/main.o
LOAD obj/apb_timer.o
LOAD obj/BSPPrint.o
LOAD obj/gic_handle_irq.o
LOAD obj/gic_irq.o
LOAD obj/irq_gic.o
LOAD obj/mprintf.o
LOAD obj/sram_on_chip_test.o
LOAD obj/timer.o
LOAD obj/uart.o
LOAD obj/croutine.o
LOAD obj/event_groups.o
LOAD obj/list.o
LOAD obj/queue.o
LOAD obj/stream_buffer.o
LOAD obj/tasks.o
LOAD obj/timers.o
LOAD obj/port.o
LOAD obj/heap_4.o
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libstdc++.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libm.a
START GROUP
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libg.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
END GROUP
START GROUP
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/lib/gcc/arm-none-eabi/9.2.1\libgcc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libc.a
LOAD F:/Tools/Compilers/gcc-arm-none-eabi-9-2019-q4-major-win32/arm-none-eabi/lib\libnosys.a
END GROUP

.text
 obj/vectors.o(.vectors)
 *(.text)

.glue_7         0x00000000        0x0
 .glue_7        0x00000000        0x0 linker stubs

.glue_7t        0x00000000        0x0
 .glue_7t       0x00000000        0x0 linker stubs

.vfp11_veneer   0x00000000        0x0
 .vfp11_veneer  0x00000000        0x0 linker stubs

.v4_bx          0x00000000        0x0
 .v4_bx         0x00000000        0x0 linker stubs

.data
 *(.data)
                0x00000000                _case_list_start = .

.test_case
 *(.test_case)
                0x00000000                _case_list_end = .

.bss
 *(.bss)

.heap           0x40018000        0x0
                0x40018000                __end__ = .
                0x40018000                PROVIDE (end = .)
 *(.heap*)
                0x40018000                __HeapLinit = .
OUTPUT(soc2018_freertos.elf elf32-littlearm)
