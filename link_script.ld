
/**
 * Linker script file for ppc4xx, modify it if needed.
 */

SECTIONS
{
/*****************************************************************/
	.text 0x40018000:
/*	.text 0xc0010000:*/
	{
		obj/vectors.o (.vectors);
		* (.text);
	}
	
	.data :
	{
		* (.data);
	}
	
	_case_list_start = .;
	.test_case : { *(.test_case) }

	_case_list_end = .;

	.bss :
	{
		* (.bss);
	}
/*
/////no memory region specified for loadable section '.rodata'/////
	.heap :
	{
	 __heap_start__ = .;
	 end =  __heap_start__ ;
	 _end = end;
	 __end = end;
	 KEEP(*(.heap))
	 __heap_end__ = .;
	 __HeapLimit = __heap_end__;
	} > REGION_HEAP
*/

	.heap (COPY):
	{
		__end__ = .;
		PROVIDE(end = .);
		*(.heap*)
		__HeapLinit = .;
	} 
 
}

